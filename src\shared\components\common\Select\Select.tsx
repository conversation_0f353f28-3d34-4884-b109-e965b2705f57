import { useState, useRef, useEffect, ReactNode, forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';

import SelectOption from './SelectOption';
import SelectGroup from './SelectGroup';
import { Icon } from '@/shared/components/common';
import ScrollArea from '../ScrollArea';
import { Z_INDEX } from '@/shared/constants/breakpoints';

// Kiểu dữ liệu cho option
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
  imageUrl?: string; // URL hình ảnh trực tiếp
  data?: Record<string, unknown>; // Dữ liệu tùy chỉnh
}

// Kiểu dữ liệu cho group
export interface SelectGroup {
  label: string;
  options: SelectOption[];
}

export interface SelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | string[] | number | number[];

  /**
   * Callback khi gi<PERSON> trị thay đổi
   */
  onChange?: (value: string | string[] | number | number[]) => void;

  /**
   * Options
   */
  options: (SelectOption | SelectGroup)[];

  /**
   * Cho phép chọn nhiều
   */
  multiple?: boolean;

  /**
   * Cho phép tìm kiếm
   */
  searchable?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Loading
   */
  loading?: boolean;

  /**
   * Custom rendering
   */
  renderOption?: (option: SelectOption) => ReactNode;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;
}

/**
 * Component Select nâng cao với nhiều tính năng
 */
const Select = forwardRef<HTMLInputElement, SelectProps>(
  (
    {
      value,
      onChange,
      options = [],
      multiple = false,
      searchable = false,
      placeholder = '',
      label,
      disabled = false,
      loading = false,
      renderOption,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme(); // Sử dụng hook theme mới
    const [isOpen, setIsOpen] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
      multiple
        ? Array.isArray(value)
          ? (value as (string | number)[])
          : []
        : value !== undefined
          ? [value as string | number]
          : []
    );
    const selectRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedValues when value prop changes
    useEffect(() => {
      if (multiple) {
        setSelectedValues(Array.isArray(value) ? (value as (string | number)[]) : []);
      } else {
        setSelectedValues(value !== undefined ? [value as string | number] : []);
      }
    }, [value, multiple]);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Focus search input when dropdown opens
    useEffect(() => {
      if (isOpen && searchable && searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, [isOpen, searchable]);

    // Handle option click
    const handleOptionClick = (optionValue: string | number) => {
      let newSelectedValues: (string | number)[];

      if (multiple) {
        // Toggle selection for multiple select
        if (selectedValues.includes(optionValue)) {
          newSelectedValues = selectedValues.filter(val => val !== optionValue);
        } else {
          newSelectedValues = [...selectedValues, optionValue];
        }
      } else {
        // Single select
        newSelectedValues = [optionValue];
        setIsOpen(false); // Close dropdown for single select
      }

      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues as string[] | number[]);
        } else {
          onChange(newSelectedValues[0]);
        }
      }

      // Clear search text when option is selected
      if (searchable) {
        setSearchText('');
      }
    };

    // Filter options based on search text
    const filterOptions = (options: (SelectOption | SelectGroup)[]) => {
      if (!searchable || !searchText) return options;

      return options
        .map(option => {
          if ('options' in option) {
            // Filter group options
            const filteredOptions = option.options.filter(opt =>
              opt.label.toLowerCase().includes(searchText.toLowerCase())
            );

            return filteredOptions.length > 0 ? { ...option, options: filteredOptions } : null;
          } else {
            // Filter single option
            return option.label.toLowerCase().includes(searchText.toLowerCase()) ? option : null;
          }
        })
        .filter(Boolean) as (SelectOption | SelectGroup)[];
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedValues.length === 0) return placeholder;

      if (multiple) {
        if (selectedValues.length === 1) {
          const selectedOption = options
            .flatMap(opt => ('options' in opt ? opt.options : [opt]))
            .find(opt => opt.value === selectedValues[0]);

          return selectedOption ? selectedOption.label : '';
        } else {
          return t('common.selected', { count: selectedValues.length });
        }
      } else {
        const selectedOption = options
          .flatMap(opt => ('options' in opt ? opt.options : [opt]))
          .find(opt => opt.value === selectedValues[0]);

        return selectedOption ? selectedOption.label : '';
      }
    };

    // Render options
    const renderOptions = () => {
      const filteredOptions = filterOptions(options);

      if (filteredOptions.length === 0) {
        return (
          <div className="px-4 py-2 text-sm text-muted">
            {t('common.noResults', 'No results found')}
          </div>
        );
      }

      return filteredOptions.map((option, index) => {
        if ('options' in option) {
          // Render group
          return (
            <SelectGroup key={`group-${index}`} label={option.label}>
              {option.options.map(groupOption => renderSingleOption(groupOption))}
            </SelectGroup>
          );
        } else {
          // Render single option
          return renderSingleOption(option);
        }
      });
    };

    // Render single option
    const renderSingleOption = (option: SelectOption) => {
      const isSelected = selectedValues.includes(option.value);

      if (renderOption) {
        return (
          <div
            key={`option-${option.value}`}
            onClick={() => !option.disabled && handleOptionClick(option.value)}
            className={`${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            {renderOption(option)}
          </div>
        );
      }

      return (
        <SelectOption
          key={`option-${option.value}`}
          value={option.value}
          label={option.label}
          icon={option.icon}
          disabled={option.disabled}
          selected={isSelected}
          onClick={() => handleOptionClick(option.value)}
          data={option.data}
        />
      );
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={multiple ? selectedValues.join(',') : selectedValues[0] || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium text-foreground mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border-0 dark:border rounded-md bg-card-muted text-foreground
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'dark:border-error' : 'dark:border-border'}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && !loading && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>

          <div className="flex items-center">
            {loading ? (
              <Icon name="loading" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-error">{error}</p>}

        {/* Helper text */}
        {helperText && !error && <p className="mt-1 text-sm text-muted">{helperText}</p>}

        {/* Dropdown */}
        {isOpen && (
          <div
            className="absolute w-full mt-1 bg-card rounded-md shadow-lg animate-fade-in select-dropdown"
            style={{ zIndex: Z_INDEX.dropdown }}
          >
            {/* Search input */}
            {searchable && (
              <div className="sticky top-0 p-2 bg-card border-b border-border">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchText}
                  onChange={e => setSearchText(e.target.value)}
                  placeholder={t('common.search', 'Search...')}
                  className="w-full px-3 py-1 text-sm border border-border rounded-md focus:outline-none focus:border-primary bg-card-muted text-foreground"
                  onClick={e => e.stopPropagation()}
                />
              </div>
            )}

            {/* Options with ScrollArea */}
            <ScrollArea
              height="auto"
              maxHeight="240px"
              autoHide={true}
              invisible={false}
              direction="vertical"
            >
              <div role="listbox" aria-multiselectable={multiple}>
                {renderOptions()}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
