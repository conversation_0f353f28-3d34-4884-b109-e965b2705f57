import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
// @ts-ignore
import eslint from 'vite-plugin-eslint';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import checker from 'vite-plugin-checker';

// https://vite.dev/config/
export default defineConfig({
  server: {
    host: true,
    cors: true,
    proxy: {},
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
    hmr: {
      port: 24678, // Sử dụng port cụ thể cho HMR
      host: 'localhost',
    },
    watch: {
      usePolling: true, // Sử dụng polling để theo dõi thay đổi file
      interval: 100, // Kiểm tra thay đổi mỗi 100ms
    },
  },
  preview: {
    host: true,
    port: 5173,
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
  },
  build: {
    chunkSizeWarningLimit: 1000, // Tăng giới hạn cảnh báo lên 1000kb
    // Không cho phép build nếu có lỗi TypeScript
    minify: true,
    sourcemap: true,
    reportCompressedSize: true,
    commonjsOptions: {
      // Đảm bảo các module CommonJS được xử lý đúng cách
      transformMixedEsModules: true,
      include: [/node_modules/],
    },
    rollupOptions: {
      external: [],
      output: {
        // Bỏ manualChunks để Vite tự động xử lý việc phân chia code
        // Điều này sẽ đảm bảo React và tất cả API của nó được tải đúng thứ tự
        manualChunks: undefined,
      },
    },
  },
  plugins: [
    react({
      // Cấu hình plugin React để đảm bảo React được xử lý đúng cách
      jsxRuntime: 'automatic',
      fastRefresh: true, // Bật Fast Refresh cho React
      babel: {
        plugins: [
          ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
        ]
      }
    }),
    eslint({
      failOnError: false, // Không dừng quá trình build nếu có lỗi ESLint
      failOnWarning: false, // Không dừng quá trình build nếu chỉ có cảnh báo
      include: ['src/**/*.ts', 'src/**/*.tsx'], // Các file cần kiểm tra
    }),
    checker({
      typescript: {
        tsconfigPath: './tsconfig.app.json', // Đường dẫn đến file tsconfig
        root: './', // Thư mục gốc của dự án
        buildMode: true, // Kiểm tra trong quá trình build
      },
      overlay: {
        initialIsOpen: true, // Hiển thị overlay khi có lỗi
        position: 'tl', // Vị trí top-left
      },
      enableBuild: true, // Bật kiểm tra khi build
      terminal: true, // Hiển thị lỗi trong terminal
    }),
    visualizer({
      open: true, // Tự động mở báo cáo sau khi build
      filename: 'dist/stats.html', // Đường dẫn lưu báo cáo
      gzipSize: true, // Hiển thị kích thước gzip
      brotliSize: true, // Hiển thị kích thước brotli
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve('./src'),
      '@components': path.resolve('./src/components'),
      '@hooks': path.resolve('./src/hooks'),
      '@contexts': path.resolve('./src/contexts'),
      '@layouts': path.resolve('./src/layouts'),
      '@pages': path.resolve('./src/pages'),
      '@services': path.resolve('./src/services'),
      '@store': path.resolve('./src/store'),
      '@styles': path.resolve('./src/styles'),
      '@types': path.resolve('./src/types'),
      '@lib': path.resolve('./src/lib'),
      '@constants': path.resolve('./src/constants'),
      '@assets': path.resolve('./src/assets'),
    },
  },
});