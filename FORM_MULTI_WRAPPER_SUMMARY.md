# FormMultiWrapper Component - Tóm tắt

## Vấn đề đã giải quyết

Bạn cần một component wrapper bọc form với:
- Thẻ div container
- Typography chứa tiêu đề
- Gi<PERSON>i quyết vấn đề tiêu đề quá to và content bị indent

## Component đã tạo

### FormMultiWrapper
**Vị trí**: `src/shared/components/common/Form/FormMultiWrapper.tsx`

**Tính năng chính**:
- Bọc form với div container và Typography title
- Tùy chỉnh variant, màu sắc, căn chỉnh cho tiêu đề
- Kiểm soát spacing, padding, background, border radius, shadow
- Hỗ trợ title border và nhiều tùy chọn styling

## Thay đổi đã thực hiện

### 1. Tối ưu Default Values
```typescript
// Trước (quá to và có padding)
titleVariant = 'h3'    // Quá lớn
spacing = 'md'         // Khoảng cách lớn
padding = 'md'         // Tạo indent không mong muốn

// Sau (compact và phù hợp)
titleVariant = 'h5'    // Vừa phải cho form title
spacing = 'sm'         // Khoảng cách nhỏ
padding = 'none'       // Không có padding để tránh indent
```

### 2. Props Interface
```typescript
interface FormMultiWrapperProps {
  title?: ReactNode;                    // Tiêu đề
  children: ReactNode;                  // Nội dung form
  className?: string;                   // Class cho container
  titleClassName?: string;              // Class cho title
  contentClassName?: string;            // Class cho content wrapper
  titleVariant?: 'h1'|'h2'|...|'h6';   // Variant Typography
  titleColor?: 'default'|'primary'|...; // Màu sắc title
  titleWeight?: 'normal'|'medium'|...;  // Độ đậm title
  titleAlign?: 'left'|'center'|'right'; // Căn chỉnh title
  spacing?: 'none'|'xs'|...|'xl';       // Khoảng cách title-content
  titleBorder?: boolean;                // Border dưới title
  padding?: 'none'|'xs'|...|'xl';       // Padding container
  background?: 'transparent'|'card'|...; // Background
  radius?: 'none'|'sm'|...|'xl';        // Border radius
  shadow?: 'none'|'sm'|...|'xl';        // Shadow
  id?: string;                          // ID container
}
```

## Cách sử dụng

### 1. Cơ bản (Như bạn đã dùng)
```tsx
<FormMultiWrapper title={t('business:product.form.createTitle')}>
  <Form ref={formRef} schema={productSchema} onSubmit={handleSubmit}>
    {/* Form content */}
  </Form>
</FormMultiWrapper>
```

### 2. Với tùy chỉnh
```tsx
<FormMultiWrapper 
  title="Thông tin sản phẩm"
  titleVariant="h4"
  titleColor="primary"
  titleBorder={true}
  spacing="md"
>
  <Form>
    {/* Form content */}
  </Form>
</FormMultiWrapper>
```

### 3. Với background và padding
```tsx
<FormMultiWrapper 
  title="Đăng ký"
  background="card"
  padding="lg"
  radius="lg"
  shadow="md"
>
  <Form>
    {/* Form content */}
  </Form>
</FormMultiWrapper>
```

## Files đã tạo/sửa

1. **FormMultiWrapper.tsx** - Component chính
2. **FormMultiWrapper.demo.tsx** - Demo component
3. **Form/index.ts** - Thêm export
4. **shared/components/common/index.ts** - Đã có export sẵn

## Import và Export

```tsx
// Import từ shared components
import { FormMultiWrapper } from '@/shared/components/common';

// Hoặc import trực tiếp từ Form
import { FormMultiWrapper } from '@/shared/components/common/Form';
```

## Kết quả

✅ **Tiêu đề vừa phải**: Sử dụng h5 thay vì h3  
✅ **Không bị indent**: Padding mặc định là 'none'  
✅ **Khoảng cách hợp lý**: Spacing mặc định là 'sm'  
✅ **Linh hoạt**: Nhiều tùy chọn customization  
✅ **Tương thích**: Hoạt động với tất cả Form components hiện có  

## Demo

Để xem demo đầy đủ, bạn có thể tạo route đến `FormMultiWrapper.demo.tsx` hoặc import component demo vào trang components showcase.

## Lưu ý

- Component này chỉ là wrapper, không ảnh hưởng đến logic form
- Có thể kết hợp với tất cả Form components khác (FormGrid, FormInline, etc.)
- Responsive và tương thích với theme system
