import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typo<PERSON>,
  <PERSON>ton,
  Stepper,
  Card,
} from '@/shared/components/common';
import { useProductImport } from '../../hooks/useProductImport';
import { ProductExcelData, ProductImportOptions, ProductColumnMapping } from '../../types/product-import.types';

// Import steps
import ProductExcelUploadStep from './steps/ProductExcelUploadStep';
import ProductColumnMappingStep from './steps/ProductColumnMappingStep';
import ProductImportPreviewStep from './steps/ProductImportPreviewStep';
import ProductImportProgressStep from './steps/ProductImportProgressStep';

interface ProductImportProps {
  onClose: () => void;
  onImportComplete?: () => void;
}

const ProductImport: React.FC<ProductImportProps> = ({
  onClose,
  onImportComplete,
}) => {
  const { t } = useTranslation(['common', 'business']);
  const {
    importState,
    setExcelData,
    updateMappings,
    startImport,
    goToPreviousStep,
    goToNextStep,
    currentStepIndex,
    resetImport,
  } = useProductImport();

  const [activeTab, setActiveTab] = useState<'file' | 'url'>('file');

  // Handle Excel file uploaded
  const handleExcelUploaded = (excelData: ProductExcelData) => {
    console.log('handleExcelUploaded called with:', excelData);
    setExcelData(excelData);
  };

  // Handle mapping complete
  const handleMappingComplete = (mappings: ProductColumnMapping[]) => {
    console.log('handleMappingComplete called with:', mappings);
    updateMappings(mappings);
    goToNextStep();
  };

  // Handle start import
  const handleStartImport = (options: ProductImportOptions) => {
    console.log('handleStartImport called with options:', options);
    // Simulate starting import with progress
    const mockProgress = {
      jobId: 'mock-job-id',
      status: 'processing' as const,
      progress: 0,
      processedRows: 0,
      totalRows: importState.excelData?.rows.length || 0,
      successCount: 0,
      errorCount: 0,
      errors: [],
      startTime: new Date().toISOString(),
    };
    startImport(mockProgress);
  };

  // Handle import progress complete
  const handleImportProgressComplete = (importedCount: number, errorCount: number) => {
    console.log('Import completed:', { importedCount, errorCount });
    onImportComplete?.();
  };

  // Handle close
  const handleClose = () => {
    resetImport();
    onClose();
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      {
        id: 'upload',
        title: t('business:product.import.steps.upload'),
        icon: 'upload'
      },
      {
        id: 'mapping',
        title: t('business:product.import.steps.mapping'),
        icon: 'link'
      },
      {
        id: 'preview',
        title: t('business:product.import.steps.preview'),
        icon: 'search'
      },
      {
        id: 'importing',
        title: t('business:product.import.steps.importing'),
        icon: 'database'
      },
    ];

    return (
      <Stepper
        steps={steps}
        currentStep={currentStepIndex}
        variant="filled"
        size="lg"
        colorScheme="primary"
        showStepIcons={true}
        showConnector={true}
        orientation="horizontal"
        responsive={true}
        animated={true}
        className="w-full"
      />
    );
  };

  // Render step content
  const renderStepContent = () => {
    console.log('Current step:', importState.step);
    switch (importState.step) {
      case 'upload':
        return (
          <ProductExcelUploadStep
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onExcelUploaded={handleExcelUploaded}
          />
        );

      case 'mapping':
        if (!importState.excelData) return null;
        return (
          <ProductColumnMappingStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            onMappingComplete={handleMappingComplete}
            onGoBack={goToPreviousStep}
          />
        );

      case 'preview':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ProductImportPreviewStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            onStartImport={handleStartImport}
            onGoBack={goToPreviousStep}
          />
        );

      case 'importing':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ProductImportProgressStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            importProgress={importState.importProgress}
            onImportComplete={handleImportProgressComplete}
            onClose={handleClose}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      {/* Header */}
      <div className="flex items-center justify-between p-1 border-b border-border">
        <Typography variant="h5">
          {t('business:product.import.title')}
        </Typography>
      </div>

      {/* Step Indicator */}
      <div className="p-4 border-b border-border">
        {renderStepIndicator()}
      </div>

      {/* Step Content */}
      <div className="flex-1">
        {renderStepContent()}
      </div>

      {/* Footer - Hide during importing, mapping and preview steps */}
      {importState.step !== 'importing' && importState.step !== 'mapping' && importState.step !== 'preview' && (
        <div className="flex items-center justify-between p-6 border-t border-border">
          <Button
            variant="outline"
            onClick={goToPreviousStep}
            disabled={importState.step === 'upload'}
          >
            {t('common:previous')}
          </Button>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleClose}>
              {t('common:cancel')}
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
};

export default ProductImport;
